import React, { useState, useEffect } from 'react'
import { useParams } from 'react-router-dom'
import FileTreePanel from '../components/files/FileTreePanel'
import ExplorerMode from '../components/files/ExplorerMode'
import MasterMode from '../components/files/MasterMode'

export interface FileTreeNode {
  path: string
  name: string
  type: 'folder' | 'file'
  isContext: boolean
  isMaster: boolean
  children?: FileTreeNode[]
  metadata?: {
    size: number
    modified: Date
    fileType: string
    isIndexed: boolean
  }
  fileCount?: number
  icon: string
  color: string
}

export interface ContextFolder {
  path: string
  name: string
  masterDocPath?: string
  fileCount: number
  lastModified: Date
  metadata: {
    description?: string
    tags?: string[]
    color?: string
    icon?: string
  }
}

const FilesPage: React.FC = () => {
  const { contextId } = useParams()
  const [viewMode, setViewMode] = useState<'explorer' | 'master'>('explorer')
  const [selectedFile, setSelectedFile] = useState<string | null>(null)
  const [fileTree, setFileTree] = useState<FileTreeNode[]>([])
  const [currentContext, setCurrentContext] = useState<ContextFolder | null>(null)
  const [isLoading, setIsLoading] = useState(true)
  const [vaultPath, setVaultPath] = useState<string>('')

  // Load vault path and initialize file tree
  useEffect(() => {
    const initializeFiles = async () => {
      try {
        setIsLoading(true)
        
        // Get vault path from electron
        if (window.electronAPI?.files) {
          const path = await window.electronAPI.files.getChatloFolderPath()
          setVaultPath(path)
          
          // Load file tree from vault
          await loadFileTree(path)
        } else {
          // Fallback for browser mode - create mock data
          setVaultPath('~/Documents/Chatlo')
          setFileTree(createMockFileTree())
        }
      } catch (error) {
        console.error('Error initializing files:', error)
      } finally {
        setIsLoading(false)
      }
    }

    initializeFiles()
  }, [contextId])

  // Load file tree from file system
  const loadFileTree = async (vaultPath: string) => {
    try {
      // For now, create a mock structure based on the design
      // This will be replaced with actual file system scanning
      const mockTree = createMockFileTree()
      setFileTree(mockTree)
      
      // Set current context if contextId is provided
      if (contextId) {
        const context = findContextInTree(mockTree, contextId)
        setCurrentContext(context)
      }
    } catch (error) {
      console.error('Error loading file tree:', error)
    }
  }

  // Create mock file tree based on design
  const createMockFileTree = (): FileTreeNode[] => {
    return [
      {
        path: '/project-alpha',
        name: 'project-alpha',
        type: 'folder',
        isContext: true,
        isMaster: false,
        fileCount: 3,
        icon: 'fa-folder',
        color: 'supplement2',
        children: [
          {
            path: '/project-alpha/master.md',
            name: 'master.md',
            type: 'file',
            isContext: false,
            isMaster: true,
            icon: 'fa-file-lines',
            color: 'primary',
            metadata: {
              size: 2048,
              modified: new Date(),
              fileType: 'markdown',
              isIndexed: true
            }
          },
          {
            path: '/project-alpha/design',
            name: 'design',
            type: 'folder',
            isContext: false,
            isMaster: false,
            fileCount: 2,
            icon: 'fa-folder',
            color: 'supplement2',
            children: []
          },
          {
            path: '/project-alpha/components',
            name: 'components',
            type: 'folder',
            isContext: false,
            isMaster: false,
            fileCount: 2,
            icon: 'fa-folder',
            color: 'supplement2',
            children: [
              {
                path: '/project-alpha/components/buttons.md',
                name: 'buttons.md',
                type: 'file',
                isContext: false,
                isMaster: false,
                icon: 'fa-file-lines',
                color: 'secondary',
                metadata: {
                  size: 1024,
                  modified: new Date(),
                  fileType: 'markdown',
                  isIndexed: true
                }
              },
              {
                path: '/project-alpha/components/forms.md',
                name: 'forms.md',
                type: 'file',
                isContext: false,
                isMaster: false,
                icon: 'fa-file-lines',
                color: 'gray-400',
                metadata: {
                  size: 512,
                  modified: new Date(),
                  fileType: 'markdown',
                  isIndexed: false
                }
              }
            ]
          },
          {
            path: '/project-alpha/tokens.json',
            name: 'tokens.json',
            type: 'file',
            isContext: false,
            isMaster: false,
            icon: 'fa-file-code',
            color: 'supplement2',
            metadata: {
              size: 4096,
              modified: new Date(),
              fileType: 'json',
              isIndexed: true
            }
          }
        ]
      }
    ]
  }

  // Find context in tree by ID
  const findContextInTree = (tree: FileTreeNode[], contextId: string): ContextFolder | null => {
    for (const node of tree) {
      if (node.isContext && node.name === contextId) {
        return {
          path: node.path,
          name: node.name,
          masterDocPath: node.children?.find(child => child.isMaster)?.path,
          fileCount: node.fileCount || 0,
          lastModified: new Date(),
          metadata: {}
        }
      }
    }
    return null
  }

  // Handle view mode toggle
  const handleViewModeChange = (mode: 'explorer' | 'master') => {
    setViewMode(mode)
    
    // If switching to master mode, select master.md if available
    if (mode === 'master' && currentContext?.masterDocPath) {
      setSelectedFile(currentContext.masterDocPath)
    }
  }

  // Handle file selection
  const handleFileSelect = (filePath: string) => {
    setSelectedFile(filePath)
  }

  if (isLoading) {
    return (
      <div className="flex-1 flex items-center justify-center bg-gray-900 text-supplement1">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto mb-4"></div>
          <p>Loading files...</p>
        </div>
      </div>
    )
  }

  return (
    <div className="flex-1 flex bg-gray-900 h-full">
      {/* File Tree Panel - 20% width */}
      <FileTreePanel
        fileTree={fileTree}
        selectedFile={selectedFile}
        viewMode={viewMode}
        onViewModeChange={handleViewModeChange}
        onFileSelect={handleFileSelect}
        vaultPath={vaultPath}
      />

      {/* Main Content Area - 80% width */}
      <div className="flex-1 flex flex-col">
        {viewMode === 'explorer' ? (
          <ExplorerMode
            selectedFile={selectedFile}
            currentContext={currentContext}
          />
        ) : (
          <MasterMode
            selectedFile={selectedFile}
            currentContext={currentContext}
          />
        )}
      </div>
    </div>
  )
}

export default FilesPage
