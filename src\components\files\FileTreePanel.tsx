import React, { useState } from 'react'
import { FileTreeNode } from '../../pages/FilesPage'

interface FileTreePanelProps {
  fileTree: FileTreeNode[]
  selectedFile: string | null
  viewMode: 'explorer' | 'master'
  onViewModeChange: (mode: 'explorer' | 'master') => void
  onFileSelect: (filePath: string) => void
  vaultPath: string
}

const FileTreePanel: React.FC<FileTreePanelProps> = ({
  fileTree,
  selectedFile,
  viewMode,
  onViewModeChange,
  onFileSelect,
  vaultPath
}) => {
  const [expandedFolders, setExpandedFolders] = useState<Set<string>>(new Set(['/project-alpha']))

  // Toggle folder expansion
  const toggleFolder = (folderPath: string) => {
    const newExpanded = new Set(expandedFolders)
    if (newExpanded.has(folderPath)) {
      newExpanded.delete(folderPath)
    } else {
      newExpanded.add(folderPath)
    }
    setExpandedFolders(newExpanded)
  }

  // Render file tree node
  const renderTreeNode = (node: FileTreeNode, depth: number = 0) => {
    const isExpanded = expandedFolders.has(node.path)
    const isSelected = selectedFile === node.path
    const indentClass = depth === 0 ? '' : `ml-${depth * 4}`

    if (node.type === 'folder') {
      return (
        <div key={node.path}>
          {/* Folder */}
          <div
            className={`file-tree-item p-2 rounded cursor-pointer flex items-center gap-2 ${indentClass} hover:bg-primary/10 transition-colors`}
            onClick={() => toggleFolder(node.path)}
          >
            <i className={`text-gray-400 text-xs w-3 ${isExpanded ? 'fa-chevron-down' : 'fa-chevron-right'}`}></i>
            <i className={`text-${node.color} text-sm fa-folder`}></i>
            <span className="text-sm text-supplement1">{node.name}</span>
            {node.fileCount && (
              <div className="ml-auto">
                <span className="w-5 h-5 bg-secondary/20 text-secondary text-xs rounded-full flex items-center justify-center font-medium">
                  {node.fileCount}
                </span>
              </div>
            )}
          </div>

          {/* Children */}
          {isExpanded && node.children && (
            <div>
              {node.children.map(child => renderTreeNode(child, depth + 1))}
            </div>
          )}
        </div>
      )
    } else {
      // File
      const fileClasses = `file-tree-item p-2 rounded cursor-pointer flex items-center gap-2 ${indentClass} transition-colors ${
        isSelected 
          ? `selected bg-${node.isMaster ? 'primary' : 'gray-700'}/20 border border-${node.isMaster ? 'primary' : 'gray-600'}/30` 
          : 'hover:bg-primary/10'
      }`

      return (
        <div
          key={node.path}
          className={fileClasses}
          onClick={() => onFileSelect(node.path)}
        >
          <div className="w-3"></div>
          <i className={`text-${node.color} text-sm ${node.icon}`}></i>
          <span className={`text-sm ${node.isMaster ? 'text-primary font-medium' : 'text-supplement1'}`}>
            {node.name}
          </span>
          {(isSelected || node.isMaster) && (
            <div className="ml-auto">
              <div className={`w-2 h-2 bg-${node.isMaster ? 'primary' : 'gray-400'} rounded-full`}></div>
            </div>
          )}
        </div>
      )
    }
  }

  return (
    <div className="w-1/5 bg-gray-800 border-r border-tertiary/50 flex flex-col">
      {/* File Tree Header */}
      <div className="p-4 border-b border-tertiary/50">
        <div className="flex items-center justify-between">
          <h3 className="font-medium text-supplement1 text-sm">Files in Vault</h3>
          <button className="p-1 hover:bg-gray-700 rounded transition-colors group relative">
            <i className="text-gray-400 text-xs fa-plus"></i>
            <div className="absolute bottom-6 right-0 bg-gray-700 text-xs px-2 py-1 rounded opacity-0 group-hover:opacity-100 transition-opacity whitespace-nowrap">
              Add File
            </div>
          </button>
        </div>
      </div>

      {/* View Toggle Buttons */}
      <div className="p-3 border-b border-tertiary/50">
        <div className="flex gap-2">
          <button
            onClick={() => onViewModeChange('explorer')}
            className={`flex-1 flex items-center justify-center gap-2 p-2 rounded-lg transition-colors text-supplement1 ${
              viewMode === 'explorer' 
                ? 'bg-gray-700/50 hover:bg-gray-700' 
                : 'hover:bg-gray-700/30'
            }`}
          >
            <i className="text-sm fa-sitemap"></i>
            <span className="text-xs font-medium">Explorer</span>
          </button>
          <button
            onClick={() => onViewModeChange('master')}
            className={`flex-1 flex items-center justify-center gap-2 p-2 rounded-lg transition-colors ${
              viewMode === 'master' 
                ? 'bg-secondary text-gray-900 hover:bg-secondary/80' 
                : 'text-supplement1 hover:bg-gray-700/30'
            }`}
          >
            <i className="text-sm fa-lightbulb"></i>
            <span className="text-xs font-medium">Master</span>
          </button>
        </div>
      </div>

      {/* File Tree */}
      <div className="flex-1 overflow-y-auto p-2">
        {fileTree.map(node => renderTreeNode(node))}
      </div>
    </div>
  )
}

export default FileTreePanel
