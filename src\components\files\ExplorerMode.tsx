import React from 'react'
import MarkdownPreviewSection from './MarkdownPreviewSection'
import RecentChatsSection from './RecentChatsSection'
import { ContextFolder } from '../../pages/FilesPage'

interface ExplorerModeProps {
  selectedFile: string | null
  currentContext: ContextFolder | null
}

const ExplorerMode: React.FC<ExplorerModeProps> = ({
  selectedFile,
  currentContext
}) => {
  return (
    <div className="flex-1 flex flex-col">
      {/* Top Row - Markdown Preview (60%) */}
      <MarkdownPreviewSection
        selectedFile={selectedFile}
        currentContext={currentContext}
      />

      {/* Bottom Row - Recent Chats (40%) */}
      <RecentChatsSection
        currentContext={currentContext}
      />
    </div>
  )
}

export default ExplorerMode
