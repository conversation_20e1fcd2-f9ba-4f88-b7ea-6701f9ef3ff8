import React from 'react'
import { ContextFolder } from '../../pages/FilesPage'

interface AIAbstractsPanelProps {
  selectedFile: string | null
  currentContext: ContextFolder | null
}

const AIAbstractsPanel: React.FC<AIAbstractsPanelProps> = ({
  selectedFile,
  currentContext
}) => {
  // Mock AI abstracts data
  const getAIAbstracts = () => {
    if (!selectedFile) return []

    if (selectedFile.includes('master.md')) {
      return [
        {
          type: 'Summary',
          content: 'Comprehensive design system documentation covering 50+ components with accessibility guidelines and responsive patterns.',
          icon: 'fa-compress'
        },
        {
          type: 'Key Insights',
          content: 'Focus on TypeScript support, WCAG 2.1 AA compliance, and scalable component architecture.',
          icon: 'fa-lightbulb'
        },
        {
          type: 'Dependencies',
          content: 'Requires @company/design-system package installation and proper component imports.',
          icon: 'fa-link'
        }
      ]
    } else if (selectedFile.includes('buttons.md')) {
      return [
        {
          type: 'Summary',
          content: 'Button component specifications with three variants: primary, secondary, and ghost buttons.',
          icon: 'fa-compress'
        },
        {
          type: 'Usage Guidelines',
          content: 'Emphasizes sparing use of primary buttons and maintaining consistent sizing and contrast.',
          icon: 'fa-circle-info'
        }
      ]
    } else if (selectedFile.includes('tokens.json')) {
      return [
        {
          type: 'Structure',
          content: 'Design tokens organized into colors, typography, and spacing categories with specific values.',
          icon: 'fa-sitemap'
        },
        {
          type: 'Color Palette',
          content: 'Five-color system: primary (#8AB0BB), secondary (#FF8383), tertiary (#1B3E68), and two supplements.',
          icon: 'fa-palette'
        }
      ]
    }

    return []
  }

  const abstracts = getAIAbstracts()

  return (
    <div className="w-80 bg-gray-800 border-l border-tertiary/50 p-4 flex flex-col">
      <div className="mb-4">
        <h4 className="font-medium text-supplement1 mb-2 text-sm">AI Abstracts</h4>
      </div>

      {!selectedFile ? (
        <div className="flex-1 flex items-center justify-center">
          <div className="text-center">
            <i className="fa-robot text-2xl text-gray-600 mb-2"></i>
            <p className="text-xs text-gray-400">Select a file to view AI abstracts</p>
          </div>
        </div>
      ) : abstracts.length === 0 ? (
        <div className="flex-1 flex items-center justify-center">
          <div className="text-center">
            <i className="fa-clock text-2xl text-gray-600 mb-2"></i>
            <p className="text-xs text-gray-400">AI abstracts processing...</p>
          </div>
        </div>
      ) : (
        <div className="space-y-3">
          {abstracts.map((abstract, index) => (
            <div
              key={index}
              className="p-3 bg-gray-700/50 rounded-lg border border-gray-600/30"
            >
              <div className="flex items-center gap-2 mb-2">
                <i className={`${abstract.icon} text-supplement2 text-sm`}></i>
                <span className="text-xs font-medium text-supplement1">{abstract.type}</span>
              </div>
              <p className="text-xs text-gray-300 leading-relaxed">
                {abstract.content}
              </p>
            </div>
          ))}
        </div>
      )}

      {/* Processing Status */}
      {selectedFile && (
        <div className="mt-4 pt-4 border-t border-tertiary/50">
          <div className="flex items-center gap-2 text-xs text-gray-400">
            <div className="w-2 h-2 bg-green-400 rounded-full"></div>
            <span>Processed by AI</span>
          </div>
        </div>
      )}
    </div>
  )
}

export default AIAbstractsPanel
