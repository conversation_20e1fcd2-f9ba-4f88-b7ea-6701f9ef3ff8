import React, { useState, useEffect } from 'react'
import MarkdownContent from './MarkdownContent'
import AIAbstractsPanel from './AIAbstractsPanel'
import { ContextFolder } from '../../pages/FilesPage'

interface MarkdownPreviewSectionProps {
  selectedFile: string | null
  currentContext: ContextFolder | null
}

const MarkdownPreviewSection: React.FC<MarkdownPreviewSectionProps> = ({
  selectedFile,
  currentContext
}) => {
  const [fileContent, setFileContent] = useState<string>('')
  const [isLoading, setIsLoading] = useState(false)

  // Load file content when selectedFile changes
  useEffect(() => {
    const loadFileContent = async () => {
      if (!selectedFile) {
        setFileContent('')
        return
      }

      setIsLoading(true)
      try {
        // For now, use mock content based on the design
        const mockContent = getMockFileContent(selectedFile)
        setFileContent(mockContent)
      } catch (error) {
        console.error('Error loading file content:', error)
        setFileContent('Error loading file content.')
      } finally {
        setIsLoading(false)
      }
    }

    loadFileContent()
  }, [selectedFile])

  // Get mock file content based on file path
  const getMockFileContent = (filePath: string): string => {
    if (filePath.includes('master.md')) {
      return `# Project Alpha Design System

A comprehensive design system for modern web applications, built with accessibility and scalability in mind.

## Overview

This design system provides a unified set of components, tokens, and guidelines to ensure consistency across all product interfaces. It includes everything from basic UI elements to complex interaction patterns.

### Key Features

- Comprehensive component library with 50+ components
- Design tokens for colors, typography, spacing, and shadows
- Accessibility guidelines and WCAG 2.1 AA compliance
- Dark and light theme support
- Responsive design patterns
- Interactive documentation and examples

## Getting Started

To begin using the design system, install the package and import the necessary components:

\`npm install @company/design-system\`

### Basic Usage

Import components as needed in your application. Each component comes with full TypeScript support and comprehensive documentation.

## Component Categories

### Foundation

- Colors and themes
- Typography scales
- Spacing and layout
- Icons and illustrations

### Components

- Buttons and actions
- Forms and inputs
- Navigation elements
- Data display
- Feedback and overlays

## Contribution Guidelines

We welcome contributions to the design system. Please follow our established patterns and ensure all new components meet our quality standards.`
    } else if (filePath.includes('buttons.md')) {
      return `# Button Components

This document outlines the button component specifications and usage guidelines.

## Button Variants

### Primary Button
- Used for main actions
- High contrast background
- Clear call-to-action

### Secondary Button
- Used for secondary actions
- Outlined style
- Lower visual priority

### Ghost Button
- Minimal styling
- Used for tertiary actions
- Subtle hover effects

## Usage Guidelines

- Use primary buttons sparingly
- Maintain consistent sizing
- Ensure proper contrast ratios
- Include loading states`
    } else if (filePath.includes('tokens.json')) {
      return `{
  "colors": {
    "primary": "#8AB0BB",
    "secondary": "#FF8383",
    "tertiary": "#1B3E68",
    "supplement1": "#D5D8E0",
    "supplement2": "#89AFBA"
  },
  "typography": {
    "fontFamily": "Inter",
    "sizes": {
      "xs": "0.75rem",
      "sm": "0.875rem",
      "base": "1rem",
      "lg": "1.125rem",
      "xl": "1.25rem"
    }
  },
  "spacing": {
    "xs": "0.25rem",
    "sm": "0.5rem",
    "md": "1rem",
    "lg": "1.5rem",
    "xl": "2rem"
  }
}`
    }
    return 'Select a file to view its content.'
  }

  return (
    <div className="h-[60%] flex border-b border-tertiary/50">
      {/* Markdown Content */}
      <MarkdownContent
        content={fileContent}
        isLoading={isLoading}
        selectedFile={selectedFile}
      />

      {/* AI Abstracts Panel */}
      <AIAbstractsPanel
        selectedFile={selectedFile}
        currentContext={currentContext}
      />
    </div>
  )
}

export default MarkdownPreviewSection
