import React from 'react'
import { useLocation, useNavigate } from 'react-router-dom'
import { useAppStore } from '../store'
import Chat<PERSON>o<PERSON>ogo from './ChatLoLogo'

interface IconBarProps {
  className?: string
}

const IconBar: React.FC<IconBarProps> = ({ className = '' }) => {
  const location = useLocation()
  const navigate = useNavigate()
  const { activeIconBarItem, setActiveIconBarItem } = useAppStore()

  // Navigation items from JSON design specification
  const navigationItems = [
    {
      name: "home",
      icon: "fa-solid fa-home",
      tooltip: "Home",
      path: "/",
      isMock: true,
      styles: "w-10 h-10 flex items-center justify-center rounded-lg hover:bg-gray-700 transition-colors text-supplement1"
    },
    {
      name: "chat",
      icon: "fa-solid fa-comment",
      tooltip: "Chat",
      path: "/",
      isMock: false,
      styles: "w-10 h-10 flex items-center justify-center rounded-lg hover:bg-gray-700 transition-colors"
    },
    {
      name: "history",
      icon: "fa-solid fa-clock-rotate-left",
      tooltip: "History",
      path: "/history",
      isMock: false,
      styles: "w-10 h-10 flex items-center justify-center rounded-lg hover:bg-gray-700 transition-colors text-supplement1"
    },
    {
      name: "files",
      icon: "fa-solid fa-folder-tree",
      tooltip: "Files",
      path: "/files",
      isMock: false,
      styles: "w-10 h-10 flex items-center justify-center rounded-lg hover:bg-gray-700 transition-colors text-supplement1"
    }
  ]

  const bottomItems = [
    {
      name: "profile",
      icon: "fa-solid fa-user",
      tooltip: "Profile",
      path: "/profile",
      isMock: true,
      styles: "w-10 h-10 flex items-center justify-center rounded-lg hover:bg-gray-700 transition-colors text-supplement1"
    },
    {
      name: "settings",
      icon: "fa-solid fa-gear",
      tooltip: "Settings",
      path: "/settings",
      isMock: false,
      styles: "w-10 h-10 flex items-center justify-center rounded-lg hover:bg-gray-700 transition-colors text-supplement1"
    }
  ]

  const handleNavigation = (path: string, itemName: string, isMock?: boolean) => {
    if (isMock) return // Don't navigate for mock icons
    setActiveIconBarItem(itemName)
    navigate(path)
  }

  // Determine active item based on current route
  const getActiveItem = () => {
    if (location.pathname === '/') return 'chat'
    if (location.pathname === '/history') return 'history'
    if (location.pathname === '/settings') return 'settings'
    if (location.pathname.startsWith('/files')) return 'files'
    return 'chat' // Default to chat
  }

  const currentActiveItem = getActiveItem()

  // Get styles for active/inactive states
  const getItemStyles = (item: any, isActive: boolean) => {
    if (isActive) {
      return `${item.styles} text-primary bg-primary/20 border-l-2 border-primary`
    }
    return `${item.styles} ${item.isMock ? 'opacity-70' : ''}`
  }

  return (
    <div className={`w-12 bg-gray-900 border-r border-tertiary flex flex-col items-center py-2 h-full ${className} hidden md:flex`}>

      {/* Top Navigation Icons - Exact spacing from design */}
      <div className="flex flex-col gap-1 mb-auto">
        {navigationItems.map((item) => (
          <button
            key={item.name}
            onClick={() => handleNavigation(item.path, item.name, item.isMock)}
            className={`
              ${getItemStyles(item, currentActiveItem === item.name)}
              group relative
            `}
            title={item.tooltip}
          >
            <i className={`${item.icon} ${currentActiveItem === item.name ? 'text-primary' : 'text-supplement1'}`}></i>
            <div className="absolute left-12 bg-gray-800 text-xs px-2 py-1 rounded opacity-0 group-hover:opacity-100 transition-opacity whitespace-nowrap z-50">
              {item.tooltip}
              {item.isMock && ' (Coming Soon)'}
            </div>
          </button>
        ))}
      </div>

      {/* Bottom Navigation Icons - Exact spacing from design */}
      <div className="flex flex-col gap-1">
        {bottomItems.map((item) => (
          <button
            key={item.name}
            onClick={() => handleNavigation(item.path, item.name, item.isMock)}
            className={`
              ${getItemStyles(item, currentActiveItem === item.name)}
              group relative
            `}
            title={item.tooltip}
          >
            <i className={`${item.icon} ${currentActiveItem === item.name ? 'text-primary' : 'text-supplement1'}`}></i>
            <div className="absolute left-12 bg-gray-800 text-xs px-2 py-1 rounded opacity-0 group-hover:opacity-100 transition-opacity whitespace-nowrap z-50">
              {item.tooltip}
              {item.isMock && ' (Coming Soon)'}
            </div>
          </button>
        ))}
      </div>
    </div>
  )
}

export default IconBar
