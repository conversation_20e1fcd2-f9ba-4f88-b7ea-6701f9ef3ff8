import React from 'react'
import { ContextFolder } from '../../pages/FilesPage'

interface RecentChatsSectionProps {
  currentContext: ContextFolder | null
}

interface ChatItem {
  id: string
  title: string
  preview: string
  timestamp: string
  messageCount: number
  isActive: boolean
}

const RecentChatsSection: React.FC<RecentChatsSectionProps> = ({
  currentContext
}) => {
  // Mock recent chats data
  const getMockChats = (): ChatItem[] => {
    return [
      {
        id: '1',
        title: 'Component Architecture Discussion',
        preview: 'How should we structure the component hierarchy for better maintainability? I\'m thinking about separating atomic components from composite ones...',
        timestamp: '2h ago',
        messageCount: 12,
        isActive: true
      },
      {
        id: '2',
        title: 'Design Token Implementation',
        preview: 'Let\'s discuss the best approach for implementing design tokens across different platforms. Should we use CSS custom properties or...',
        timestamp: '1d ago',
        messageCount: 8,
        isActive: false
      },
      {
        id: '3',
        title: 'Accessibility Guidelines Review',
        preview: 'Need to review our current accessibility implementation and ensure we meet WCAG 2.1 AA standards for all components...',
        timestamp: '3d ago',
        messageCount: 15,
        isActive: false
      }
    ]
  }

  const recentChats = getMockChats()

  const handleChatClick = (chatId: string) => {
    console.log('Navigate to chat:', chatId)
    // This would navigate to the chat page with the specific conversation
  }

  const handleReplyClick = (chatId: string, event: React.MouseEvent) => {
    event.stopPropagation()
    console.log('Quick reply to chat:', chatId)
    // This would open a quick reply interface
  }

  return (
    <div className="h-[40%] bg-gray-800/50">
      {/* Chats Header */}
      <div className="p-4 border-b border-tertiary/50">
        <div className="flex items-center justify-between">
          <h3 className="font-medium text-supplement1">Recent Chats</h3>
          <button className="text-xs text-primary hover:text-primary/80 transition-colors">
            View All
          </button>
        </div>
      </div>

      {/* Chats List */}
      <div className="flex-1 overflow-y-auto p-4">
        {recentChats.length === 0 ? (
          <div className="flex items-center justify-center h-full">
            <div className="text-center">
              <i className="fa-comments text-2xl text-gray-600 mb-2"></i>
              <p className="text-sm text-gray-400">No recent chats</p>
              <p className="text-xs text-gray-500">Start a conversation to see it here</p>
            </div>
          </div>
        ) : (
          <div className="space-y-3">
            {recentChats.map((chat) => (
              <div
                key={chat.id}
                className="p-4 bg-gray-700/50 rounded-lg hover:bg-gray-700 cursor-pointer transition-colors"
                onClick={() => handleChatClick(chat.id)}
              >
                <div className="flex items-start justify-between mb-2">
                  <div className="flex-1">
                    <h5 className="font-medium text-sm text-supplement1 mb-1">
                      {chat.title}
                    </h5>
                    <p className="text-xs text-gray-400 line-clamp-2 leading-relaxed">
                      {chat.preview}
                    </p>
                  </div>
                  <div className="flex items-center gap-2 ml-3">
                    <span className="text-xs text-gray-500">{chat.timestamp}</span>
                    <button
                      onClick={(e) => handleReplyClick(chat.id, e)}
                      className="p-1 hover:bg-gray-600 rounded transition-colors"
                      title="Quick Reply"
                    >
                      <i className="text-gray-400 text-xs fa-reply"></i>
                    </button>
                  </div>
                </div>
                <div className="flex items-center gap-2">
                  <div className={`w-2 h-2 ${chat.isActive ? 'bg-primary' : 'bg-gray-500'} rounded-full`}></div>
                  <span className="text-xs text-gray-400">{chat.messageCount} messages</span>
                </div>
              </div>
            ))}
          </div>
        )}
      </div>
    </div>
  )
}

export default RecentChatsSection
