# Files UI Layout Documentation

## Overview
The Files UI implements a comprehensive file management interface based on the file-system context vault philosophy. It provides a three-panel layout optimized for browsing, previewing, and interacting with context files while maintaining seamless integration with the chat system.

## Layout Structure

### Main Container
```
┌─────────────────────────────────────────────────────────────────┐
│ Window Top Bar (h-6)                                           │
├─────────────────────────────────────────────────────────────────┤
│ Top Navigation Bar (h-12)                                      │
├─────┬───────────────────────────────────────────────────────────┤
│ Icon│ Main Files Content (flex-1)                              │
│ Bar │ ┌─────────────┬─────────────────────────────────────────┐ │
│(w-12│ │ File Tree   │ Right Panel (flex-1)                   │ │
│     │ │ Panel       │ ┌─────────────────────────────────────┐ │ │
│     │ │ (w-1/5)     │ │ Markdown Preview (h-[60%])          │ │ │
│     │ │             │ │                                     │ │ │
│     │ │             │ ├─────────────────────────────────────┤ │ │
│     │ │             │ │ Recent Chats (h-[40%])              │ │ │
│     │ │             │ │                                     │ │ │
│     │ │             │ └─────────────────────────────────────┘ │ │
│     │ └─────────────┴─────────────────────────────────────────┘ │
└─────┴───────────────────────────────────────────────────────────┘
```

## Component Specifications

### 1. File Tree Panel (Left - 20% width)

#### Header Section
- **Title**: "Files in Vault" with supplement1 color
- **Add Button**: Plus icon with hover tooltip "Add File"
- **Border**: Bottom border with tertiary/50 color

#### View Toggle Buttons
- **Explorer Button**: Active state with gray-700/50 background
  - Icon: `fa-sitemap`
  - Text: "Explorer"
- **Master But<PERSON>**: Secondary state with secondary background
  - Icon: `fa-lightbulb` 
  - Text: "Master"

#### File Tree Structure
```typescript
interface FileTreeNode {
  type: 'folder' | 'file'
  name: string
  path: string
  isExpanded?: boolean
  isSelected?: boolean
  children?: FileTreeNode[]
  fileCount?: number
  icon: string
  color: string
}
```

#### Visual States
- **Hover**: `rgba(138, 176, 187, 0.1)` background
- **Selected**: `rgba(138, 176, 187, 0.2)` background + left border
- **Master.md**: Special highlighting with primary color and dot indicator

### 2. Markdown Preview Section (Top Right - 60% height)

#### Content Area
- **Styling**: Full markdown rendering with custom CSS
- **Typography**: Inter font family with proper hierarchy
- **Colors**: 
  - H1: `#D5D8E0` (supplement1)
  - H2: `#D5D8E0` (supplement1) 
  - H3: `#89AFBA` (supplement2)
  - Body: `#9CA3AF` (gray-400)
  - Code: `#8AB0BB` (primary) on `#374151` background

#### Quick Actions Panel (Right Sidebar - w-80)
```typescript
interface QuickAction {
  icon: string
  title: string
  description: string
  color: 'primary' | 'secondary' | 'supplement2'
  action: () => void
}

const quickActions = [
  {
    icon: 'fa-circle-question',
    title: 'Ask about this file',
    description: 'Get insights',
    color: 'primary'
  },
  {
    icon: 'fa-compress', 
    title: 'Summarize',
    description: 'Brief summary',
    color: 'secondary'
  },
  {
    icon: 'fa-pen-to-square',
    title: 'Edit content', 
    description: 'Make improvements',
    color: 'supplement2'
  }
]
```

#### Chat Input Area
- **Textarea**: Multi-line input with placeholder "Ask anything about this file..."
- **Send Button**: Primary colored button with hover effects
- **Styling**: Gray-700 background with tertiary border

### 3. Recent Chats Section (Bottom Right - 40% height)

#### Header
- **Title**: "Recent Chats" with supplement1 color
- **View All Link**: Primary colored link with hover effects

#### Chat Items
```typescript
interface ChatItem {
  id: string
  title: string
  preview: string
  timestamp: string
  messageCount: number
  isActive: boolean
}
```

#### Visual Design
- **Background**: `gray-700/50` with hover state `gray-700`
- **Layout**: Flex layout with title, preview, timestamp, and actions
- **Indicators**: Primary colored dot for active status
- **Actions**: Reply button with hover effects

## Color System Integration

### Primary Colors
- **Primary**: `#8AB0BB` - Used for active states, buttons, highlights
- **Secondary**: `#FF8383` - Used for accent elements, warnings
- **Tertiary**: `#1B3E68` - Used for borders, structural elements

### Supplement Colors  
- **Supplement1**: `#D5D8E0` - Used for primary text, headings
- **Supplement2**: `#89AFBA` - Used for secondary text, icons

### Background Colors
- **Main**: `gray-900` - Primary background
- **Panel**: `gray-800` - Secondary panels
- **Hover**: `gray-700` - Interactive hover states

## Responsive Behavior

### Breakpoints
- **Desktop**: Full three-panel layout
- **Tablet**: Collapsible file tree panel
- **Mobile**: Stack panels vertically with tabs

### Interaction Patterns
- **File Selection**: Updates preview and highlights tree item
- **Folder Toggle**: Expand/collapse with chevron rotation
- **Quick Actions**: Trigger appropriate chat or edit flows
- **Chat Integration**: Seamless transition to chat with file context

## State Management

### File Tree State
```typescript
interface FileTreeState {
  expandedFolders: Set<string>
  selectedFile: string | null
  fileTree: FileTreeNode[]
  isLoading: boolean
}
```

### Preview State
```typescript
interface PreviewState {
  currentFile: string | null
  content: string
  isLoading: boolean
  error: string | null
}
```

### Chat Integration State
```typescript
interface ChatIntegrationState {
  recentChats: ChatItem[]
  selectedContext: string | null
  quickActionLoading: boolean
}
```

## Implementation Notes

### File System Integration
- Real-time file watching for updates
- Direct file system access for content
- Context folder scanning and indexing
- Master.md prioritization and highlighting

### Performance Optimizations
- Virtual scrolling for large file trees
- Lazy loading of file content
- Debounced search and filtering
- Cached file metadata

### Accessibility
- Keyboard navigation for file tree
- Screen reader support for all interactive elements
- Focus management for modal interactions
- High contrast mode support

This layout provides a comprehensive file management experience while maintaining the ChatLo design system consistency and supporting the file-based context vault philosophy outlined in the implementation plan.

## Development Implementation Guide

### Component Architecture
```
src/pages/FilesPage.tsx
├── FileTreePanel.tsx
│   ├── FileTreeHeader.tsx
│   ├── ViewToggleButtons.tsx
│   └── FileTreeView.tsx
├── MarkdownPreviewSection.tsx
│   ├── MarkdownContent.tsx
│   └── QuickActionsPanel.tsx
└── RecentChatsSection.tsx
    ├── ChatHeader.tsx
    └── ChatList.tsx
```

### Key Features from Design Reference

#### Navigation Breadcrumb
- **Location**: Top navigation bar center
- **Format**: "Project Alpha - Design System" with folder icon
- **Styling**: Tertiary background with supplement1 text
- **Navigation**: Left/right chevrons for context switching

#### File Tree Interactions
- **Folder Icons**: Supplement2 colored folder icons
- **File Icons**: Color-coded by type (primary for master.md, secondary for active files)
- **Counters**: Small circular badges showing file counts
- **Expansion**: Chevron icons that rotate on folder toggle

#### Master Document Focus
- **Special Treatment**: master.md files get primary color highlighting
- **Active Indicator**: Primary colored dot for currently selected file
- **Border**: Left border accent for selected items

#### Quick Actions Integration
- **Context Awareness**: Actions change based on selected file type
- **AI Integration**: Direct connection to chat system with file context
- **Visual Feedback**: Loading states and hover animations

### Technical Requirements

#### File System Service Integration
```typescript
// Required service methods
interface FileSystemService {
  scanContextFolder(path: string): Promise<FileTreeNode[]>
  readFileContent(path: string): Promise<string>
  watchFileChanges(path: string, callback: (event: FileEvent) => void): void
  createFile(path: string, content: string): Promise<void>
  deleteFile(path: string): Promise<void>
}
```

#### Chat Context Integration
```typescript
// Chat integration methods
interface ChatContextService {
  createChatWithFileContext(filePath: string, contextPath: string): Promise<string>
  getRecentChatsForContext(contextPath: string): Promise<ChatItem[]>
  linkChatToFile(chatId: string, filePath: string): Promise<void>
}
```

#### State Synchronization
- Real-time file system watching
- Automatic preview updates on file changes
- Context switching preserves selection state
- Chat history filtered by current context

This comprehensive layout documentation provides all necessary details for implementing the Files UI according to the design specifications and integration requirements.
