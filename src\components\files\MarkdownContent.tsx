import React from 'react'

interface MarkdownContentProps {
  content: string
  isLoading: boolean
  selectedFile: string | null
}

const MarkdownContent: React.FC<MarkdownContentProps> = ({
  content,
  isLoading,
  selectedFile
}) => {
  // Simple markdown-to-HTML converter for basic formatting
  const renderMarkdown = (text: string) => {
    if (!text) return ''

    let html = text
      // Headers
      .replace(/^### (.*$)/gim, '<h3>$1</h3>')
      .replace(/^## (.*$)/gim, '<h2>$1</h2>')
      .replace(/^# (.*$)/gim, '<h1>$1</h1>')
      // Bold
      .replace(/\*\*(.*)\*\*/gim, '<strong>$1</strong>')
      // Italic
      .replace(/\*(.*)\*/gim, '<em>$1</em>')
      // Code blocks
      .replace(/```([\s\S]*?)```/gim, '<pre><code>$1</code></pre>')
      // Inline code
      .replace(/`([^`]*)`/gim, '<code>$1</code>')
      // Lists
      .replace(/^\- (.*$)/gim, '<li>$1</li>')
      // Paragraphs
      .replace(/\n\n/gim, '</p><p>')

    // Wrap in paragraph tags and handle lists
    html = '<p>' + html + '</p>'
    html = html.replace(/<p><li>/gim, '<ul><li>')
    html = html.replace(/<\/li><\/p>/gim, '</li></ul>')
    html = html.replace(/<p><\/p>/gim, '')

    return html
  }

  if (isLoading) {
    return (
      <div className="flex-1 overflow-y-auto p-6 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-primary mx-auto mb-2"></div>
          <p className="text-sm text-gray-400">Loading content...</p>
        </div>
      </div>
    )
  }

  if (!selectedFile) {
    return (
      <div className="flex-1 overflow-y-auto p-6 flex items-center justify-center">
        <div className="text-center">
          <i className="fa-file-lines text-4xl text-gray-600 mb-4"></i>
          <p className="text-supplement1 mb-2">No file selected</p>
          <p className="text-sm text-gray-400">Select a file from the tree to view its content</p>
        </div>
      </div>
    )
  }

  return (
    <div className="flex-1 overflow-y-auto p-6">
      <div
        className="markdown-content"
        dangerouslySetInnerHTML={{ __html: renderMarkdown(content) }}
      />

      <style dangerouslySetInnerHTML={{
        __html: `
          .markdown-content h1 {
            color: #D5D8E0;
            font-size: 1.5rem;
            font-weight: 600;
            margin-bottom: 1rem;
          }
          .markdown-content h2 {
            color: #D5D8E0;
            font-size: 1.25rem;
            font-weight: 500;
            margin-bottom: 0.75rem;
            margin-top: 1.5rem;
          }
          .markdown-content h3 {
            color: #89AFBA;
            font-size: 1.125rem;
            font-weight: 500;
            margin-bottom: 0.5rem;
            margin-top: 1rem;
          }
          .markdown-content p {
            color: #9CA3AF;
            line-height: 1.6;
            margin-bottom: 1rem;
          }
          .markdown-content ul {
            color: #9CA3AF;
            margin-left: 1.5rem;
            margin-bottom: 1rem;
          }
          .markdown-content li {
            margin-bottom: 0.25rem;
          }
          .markdown-content code {
            background-color: #374151;
            color: #8AB0BB;
            padding: 0.125rem 0.25rem;
            border-radius: 0.25rem;
            font-size: 0.875rem;
          }
          .markdown-content pre {
            background-color: #1F2937;
            color: #D5D8E0;
            padding: 1rem;
            border-radius: 0.5rem;
            margin: 1rem 0;
            overflow-x: auto;
          }
          .markdown-content pre code {
            background: none;
            padding: 0;
          }
        `
      }} />
    </div>
  )
}

export default MarkdownContent
