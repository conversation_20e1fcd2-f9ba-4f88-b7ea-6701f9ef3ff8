import React, { useState, useEffect } from 'react'
import MarkdownContent from './MarkdownContent'
import { ContextFolder } from '../../pages/FilesPage'
import { useAppStore } from '../../store'

interface MasterModeProps {
  selectedFile: string | null
  currentContext: ContextFolder | null
}

const MasterMode: React.FC<MasterModeProps> = ({
  selectedFile,
  currentContext
}) => {
  const { showArtifacts, setShowArtifacts } = useAppStore()
  const [masterContent, setMasterContent] = useState<string>('')
  const [isLoading, setIsLoading] = useState(false)

  // Load master document content
  useEffect(() => {
    const loadMasterContent = async () => {
      setIsLoading(true)
      try {
        // Use the master.md content from the mock data
        const mockMasterContent = `# Project Alpha Design System

A comprehensive design system for modern web applications, built with accessibility and scalability in mind.

## Overview

This design system provides a unified set of components, tokens, and guidelines to ensure consistency across all product interfaces. It includes everything from basic UI elements to complex interaction patterns.

### Key Features

- Comprehensive component library with 50+ components
- Design tokens for colors, typography, spacing, and shadows
- Accessibility guidelines and WCAG 2.1 AA compliance
- Dark and light theme support
- Responsive design patterns
- Interactive documentation and examples

## Getting Started

To begin using the design system, install the package and import the necessary components:

\`npm install @company/design-system\`

### Basic Usage

Import components as needed in your application. Each component comes with full TypeScript support and comprehensive documentation.

## Component Categories

### Foundation

- Colors and themes
- Typography scales
- Spacing and layout
- Icons and illustrations

### Components

- Buttons and actions
- Forms and inputs
- Navigation elements
- Data display
- Feedback and overlays

## Contribution Guidelines

We welcome contributions to the design system. Please follow our established patterns and ensure all new components meet our quality standards.

## Recent Updates

- Added new button variants
- Updated color tokens
- Improved accessibility documentation
- Enhanced TypeScript definitions

## Next Steps

- Implement dark mode variants
- Add animation guidelines
- Create Figma component library
- Develop testing utilities`

        setMasterContent(mockMasterContent)
      } catch (error) {
        console.error('Error loading master content:', error)
        setMasterContent('Error loading master document.')
      } finally {
        setIsLoading(false)
      }
    }

    loadMasterContent()
  }, [currentContext])

  // Show artifacts when entering master mode
  useEffect(() => {
    setShowArtifacts(true)

    // Add some mock artifacts for demonstration
    const { addArtifact } = useAppStore.getState()

    // Add mock artifacts if none exist
    const currentArtifacts = useAppStore.getState().artifacts.artifacts
    if (currentArtifacts.length === 0) {
      addArtifact({
        id: 'design-tokens',
        type: 'json',
        title: 'Design Tokens',
        content: JSON.stringify({
          colors: {
            primary: "#8AB0BB",
            secondary: "#FF8383",
            tertiary: "#1B3E68"
          },
          typography: {
            fontFamily: "Inter",
            sizes: { xs: "0.75rem", sm: "0.875rem", base: "1rem" }
          }
        }, null, 2),
        metadata: {
          language: 'json',
          createdAt: new Date().toISOString(),
          messageId: 'master-doc',
          size: 256
        },
        isActive: false
      })

      addArtifact({
        id: 'component-guide',
        type: 'markdown',
        title: 'Component Usage Guide',
        content: `# Component Usage Guide

## Button Components

### Primary Button
\`\`\`jsx
<Button variant="primary">Click me</Button>
\`\`\`

### Secondary Button
\`\`\`jsx
<Button variant="secondary">Secondary action</Button>
\`\`\`

## Form Components

### Input Field
\`\`\`jsx
<Input placeholder="Enter text..." />
\`\`\`
`,
        metadata: {
          createdAt: new Date().toISOString(),
          messageId: 'master-doc',
          size: 512
        },
        isActive: false
      })
    }

    // Cleanup: hide artifacts when leaving master mode
    return () => {
      setShowArtifacts(false)
    }
  }, [setShowArtifacts])

  return (
    <div className="flex-1 flex">
      {/* Master Document Preview - Full width when artifacts are hidden */}
      <div className={`${showArtifacts ? 'flex-1' : 'w-full'} flex flex-col`}>
        <div className="p-4 border-b border-tertiary/50 bg-gray-800/30">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-3">
              <i className="fa-file-lines text-primary text-lg"></i>
              <div>
                <h3 className="font-medium text-supplement1">Master Document</h3>
                <p className="text-xs text-gray-400">
                  {currentContext?.name || 'Project Alpha'} - Design System Overview
                </p>
              </div>
            </div>
            <div className="flex items-center gap-2">
              <button
                onClick={() => setShowArtifacts(!showArtifacts)}
                className={`p-2 rounded-lg transition-colors ${
                  showArtifacts 
                    ? 'bg-primary/20 text-primary border border-primary/30' 
                    : 'bg-gray-700 text-gray-300 hover:bg-gray-600'
                }`}
                title={showArtifacts ? 'Hide Artifacts' : 'Show Artifacts'}
              >
                <i className="fa-bell text-sm"></i>
              </button>
              <button className="p-2 bg-gray-700 text-gray-300 hover:bg-gray-600 rounded-lg transition-colors">
                <i className="fa-edit text-sm"></i>
              </button>
            </div>
          </div>
        </div>

        {/* Master Document Content */}
        <MarkdownContent
          content={masterContent}
          isLoading={isLoading}
          selectedFile="/project-alpha/master.md"
        />
      </div>

      {/* Artifacts Sidebar is handled by the existing ArtifactsSidebar component in App.tsx */}
      {/* It will automatically show/hide based on the showArtifacts state */}
    </div>
  )
}

export default MasterMode
